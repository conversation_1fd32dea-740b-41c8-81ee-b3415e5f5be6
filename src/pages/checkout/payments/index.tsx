import { GetServerSideProps } from 'next';
import nookies from 'nookies';

import { cookieConfig } from '~/components/modules/Cart/CartSummary.constants';
import { PaymentOptionsContainerProps } from '~/components/pages/CheckoutPage/Payments/PaymentOptions.types';
import PaymentOptionsContainer from '~/components/pages/CheckoutPage/Payments/PaymentOptionsContainer';
import { SiteCartAppointmentApiResponse } from '~/data/models/SiteCartAppointmentResponse';
import { SiteCartBillingApiResponse } from '~/data/models/SiteCartBillingResponse';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import {
  ShippingType,
  SiteCartShippingApiResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import WithErrorPageHandling, {
  PageResponse,
} from '~/hocs/WithPageErrorHandling';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetCartAppointment } from '~/lib/backend/checkout/cart-appointment';
import { backendGetCartBillingLegacy } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShippingLegacy } from '~/lib/backend/checkout/cart-shipping';
import { backendGetCartSummaryLegacy } from '~/lib/backend/checkout/cart-summary';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { AsyncResponse } from '~/lib/fetch/index.types';
import { getUserIp } from '~/lib/utils/ip';
import { getStringifiedParams } from '~/lib/utils/routes';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

function PaymentOptions(props: PaymentOptionsContainerProps) {
  return <PaymentOptionsContainer {...props} />;
}

export const getServerSideProps: GetServerSideProps<
  PageResponse<PaymentOptionsContainerProps>
> = async (context) => {
  backendBootstrap({ request: context.req });
  const userIp = getUserIp(context.req);
  const queryParams = getStringifiedParams(context.query);
  const { cartId, widgetSource, cartid, widgetType, widgetSourceId } =
    queryParams;
  let storedCartId;
  const cookies = nookies.get(context);

  // Refer to useWidgetSource.ts for latest types
  if (
    widgetSource === 'pirelli' ||
    widgetType === 'installer-widget' ||
    widgetType === 'affiliate-widget' ||
    widgetType === 'manufacturer-widget'
  ) {
    storedCartId = cartId;
    nookies.set(context, COOKIES.CART_ID, String(cartId), cookieConfig);
  } else {
    storedCartId = cartId ?? cartid ?? cookies[COOKIES.CART_ID] ?? '';
  }

  if (!storedCartId) {
    // FND-1606: redirect to home page directly if cartId is not found.
    return {
      redirect: {
        permanent: false,
        destination: '/',
      },
    };
  }

  let cartShippingResponse: AsyncResponse<SiteCartShippingApiResponse> | null =
    null;

  let cartAppointment: AsyncResponse<SiteCartAppointmentApiResponse> | null =
    null;

  let cartBillingResponse: AsyncResponse<SiteCartBillingApiResponse> | null =
    null;

  const headerCookie = context.req?.headers?.cookie ?? '';
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }

  const accountTypeCompany = cookies[COOKIES.OTS_ACCOUNT_TYPE_COMPANY];
  const widgetId = cookies[COOKIES.OTS_WIDGET_ID];
  const userRegion = cookies[COOKIES.REGION];
  const userZip = cookies[COOKIES.ZIP];
  const cartSummaryResponse: AsyncResponse<SiteCartSummaryResponse> =
    await backendGetCartSummaryLegacy({
      id: storedCartId,
      query: {
        ...(widgetSource && widgetSourceId
          ? { widgetSource, widgetSourceId }
          : { ssoUid: ssoUserId || '' }),
        accountTypeCompany,
        userRegion,
        userZip,
        widgetId,
      },
    });

  if (!cartSummaryResponse.isSuccess) {
    const errorStatusCode = cartSummaryResponse.error.statusCode;

    context.res.statusCode = errorStatusCode;
    return {
      redirect: {
        permanent: false,
        destination: '/',
      },
    };
  }

  [cartShippingResponse, cartBillingResponse] = await Promise.all([
    backendGetCartShippingLegacy({
      cartId: storedCartId,
    }),
    backendGetCartBillingLegacy({ cartId: storedCartId }),
  ]);

  if (
    cartShippingResponse.isSuccess &&
    cartShippingResponse.data.siteCartShippingResponse.shippingOption ===
      ShippingType.INSTALLER
  ) {
    cartAppointment = await backendGetCartAppointment({
      cartId: storedCartId,
    });
  }

  const cartBilling =
    cartBillingResponse &&
    cartBillingResponse.isSuccess &&
    cartBillingResponse.data
      ? cartBillingResponse.data.siteCartBillingResponse.cartBilling
      : null;

  const initialBillingInfo = {
    addressLine1: cartBilling?.addressLine1 ?? '',
    addressLine2: cartBilling?.addressLine2 ?? '',
    city: cartBilling?.city ?? '',
    firstName: cartBilling?.firstName ?? '',
    lastName: cartBilling?.lastName ?? '',
    phone: cartBilling?.phone ?? '',
    phoneType: cartBilling?.phoneType ?? PHONE_TYPE.MOBILE,
    poNumber: cartBilling?.poNumber ?? '',
    state: cartBilling?.state ?? '',
    zip: cartBilling?.zip ?? '',
  };

  return {
    props: {
      initialBillingInfo,
      siteCartAppointment:
        cartAppointment && cartAppointment.isSuccess
          ? cartAppointment.data.SiteCartAppointment
          : null,
      siteCartShipping:
        cartShippingResponse && cartShippingResponse.isSuccess
          ? cartShippingResponse.data.siteCartShippingResponse
          : null,
      siteCartSummary: cartSummaryResponse.data,
      userIp: userIp || null,
    },
  };
};

export default WithErrorPageHandling(PaymentOptions);
