import { NextApiRequest, NextApiResponse } from 'next';

import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import {
  backendCreateCartServices,
  backendGetCartServices,
} from '~/lib/backend/checkout/cart-services';
import logger from '~/lib/helpers/logger';
import { getAndCheckUserSessionId } from '~/lib/utils/api';
import { getStringifiedParams } from '~/lib/utils/routes';

const cartServicesApi = async (
  request: NextApiRequest,
  response: NextApiResponse<SiteCartSummaryResponse>,
) => {
  backendBootstrap({ request });

  const { id, ...rest } = getStringifiedParams(request.query);

  switch (request.method?.toLowerCase()) {
    case 'post': {
      if (!id) {
        response.status(400).end();
        return;
      }

      const userSessionId = getAndCheckUserSessionId(request, response);

      if (userSessionId) {
        try {
          const res = await backendCreateCartServices({
            id,
            userTime: rest.userTime,
            vwo_user: rest.vwo_user,
            input: {
              sessionId: userSessionId,
              ...request.body,
            },
          });
          if (res.isSuccess && res.data) {
            response.json(res.data);
            return;
          }

          response.status(500).end();
        } catch (error) {
          logger.error('Error in cart services API:', error);
          response.status(500).end();
        }
      }
      break;
    }
    case 'get': {
      if (!id) {
        response.status(400).end();
        return;
      }

      const userSessionId = getAndCheckUserSessionId(request, response);

      if (userSessionId) {
        try {
          const res = await backendGetCartServices({
            id,
            userTime: rest.userTime,
            vwo_user: rest.vwo_user,
            input: {
              sessionId: userSessionId,
              ...request.body,
            },
          });
          if (res.isSuccess && res.data) {
            response.json(res.data);
            return;
          }

          response.status(500).end();
        } catch (error) {
          logger.error('Error in cart services API:', error);
          response.status(500).end();
        }
      }
      break;
    }
    default:
      response.status(400).end();
      break;
  }
};

export default cartServicesApi;
