import { SiteCartAppointmentRequest } from '~/data/models/SiteCartAppointmentRequest';
import { SiteCartAppointmentApiResponse } from '~/data/models/SiteCartAppointmentResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartAppointment(
  {
    cartId,
    query,
  }: {
    cartId: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartAppointmentApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/appointments',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendCreateCartAppointment(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SiteCartAppointmentRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentApiResponse,
    SiteCartAppointmentRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/appointments',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
  });
}

export async function backendUpdateCartAppointment(
  {
    cartId,
    query,
    input,
  }: {
    cartId: string;
    input: SiteCartAppointmentRequest;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentApiResponse,
    SiteCartAppointmentRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/appointments',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendDeleteCartAppointment(
  {
    cartId,
  }: {
    cartId: string;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentApiResponse,
    SiteCartAppointmentRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/appointments',
    extraQueryParams,
    includeAuthorization: true,
    method: 'delete',
    params: {
      cartId,
    },
  });
}
