import { SiteCartBillingRequest } from '~/data/models/SiteCartBillingRequest';
import { SiteCartBillingApiResponse } from '~/data/models/SiteCartBillingResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartBilling(
  { cartId }: { cartId: string },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartBillingApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/billing',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    params: {
      cartId,
    },
  });
}

export async function backendCreateCartBilling(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SiteCartBillingRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartBillingApiResponse,
    SiteCartBillingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/billing',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
  });
}

export async function backendUpdateCartBilling(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SiteCartBillingRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartBillingApiResponse,
    SiteCartBillingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/billing',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
    params: {
      cartId,
    },
  });
}
