import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';
import { AsyncResponse } from '~/lib/fetch/index.types';

interface BackendCreateCartServicesProps {
  id: string;
  input: {
    installerId: number;
    services: Array<{
      imagesUrl: string;
      notes: string;
      serviceId: number;
      serviceName: string;
    }>;
    sessionId: string;
  };
  userTime?: string;
  vwo_user?: string;
}

export async function backendCreateCartServices(
  props: BackendCreateCartServicesProps,
  extraQueryParams?: Record<string, string>,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  const query = {
    ...(props.userTime && { userTime: props.userTime }),
    ...(props.vwo_user && { vwo_user: props.vwo_user }),
  };

  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    typeof props.input
  >({
    endpoint: `/v2/site/cart/${props.id}/services`,
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: props.input,
    method: 'post',
    query,
  });
}

export async function backendGetCartServices(
  props: BackendCreateCartServicesProps,
  extraQueryParams?: Record<string, string>,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  const query = {
    ...(props.userTime && { userTime: props.userTime }),
    ...(props.vwo_user && { vwo_user: props.vwo_user }),
  };

  return await fetchWithErrorHandling<SiteCartSummaryResponse>({
    endpoint: `/v2/site/cart/${props.id}/services`,
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    query,
  });
}
