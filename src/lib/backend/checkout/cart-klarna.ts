import { CartKlarnaResponse } from '~/data/models/CartKlarnaResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartKlarna(
  {
    cartId,
    gateway,
  }: {
    cartId: string;
    gateway: string;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<CartKlarnaResponse>({
    endpoint: '/v2/site/payment/session/{cartId}',
    extraQueryParams,
    includeAuthorization: true,
    method: 'post',
    params: {
      cartId,
    },
    query: {
      gateway,
    },
  });
}
