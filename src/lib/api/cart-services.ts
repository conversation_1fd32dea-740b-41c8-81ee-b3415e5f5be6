import { fetchWithErrorHandling } from '../fetch';

interface CartServicePayload {
  imagesUrl: string;
  notes: string;
  serviceId: number;
  serviceName: string;
}

interface CartServicesRequest {
  installerId: number;
  services: CartServicePayload[];
}

// POST: Store services for a cart
export async function apiCreateCartServices(
  cartId: string,
  payload: CartServicesRequest,
) {
  return await fetchWithErrorHandling({
    endpoint: '/checkout/cart-services',
    includeAuthorization: true,
    includeUserTime: true,
    includeUserVwo: true,
    jsonBody: payload,
    method: 'post',
    query: { id: cartId },
  });
}

// GET: Fetch services for a cart
export async function apiGetCartServices({ cartUuid }: { cartUuid: string }) {
  return await fetchWithErrorHandling({
    endpoint: '/checkout/cart-services',
    includeAuthorization: true,
    includeUserTime: true,
    includeUserVwo: true,
    method: 'get',
    query: { id: cartUuid },
  });
}
