import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';

import OrderConfirmationContainer, {
  ServerData,
} from '~/components/pages/CheckoutPage/OrderConfirmation/OrderConfirmationContainer';
import { AppRouteCheckoutOrderConfirmationPageParams } from '~/data/AppRoutePageParams';
import { backendGetSSOToken } from '~/lib/backend/account/sso-token';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetOrder } from '~/lib/backend/checkout/cart-order';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { backendGetCartSummary } from '~/lib/backend/checkout/cart-summary';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import {
  SSOTokenInput,
  SSOTokenResponse,
  SSOUserIdResponse,
} from '~/lib/constants/sso.types';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import {
  isLocal as isLocalDeployment,
  isProductionDeploy,
} from '~/lib/utils/deploy';
import { getStringifiedParams } from '~/lib/utils/routes';

const isLocal = isLocalDeployment();
const clientSecret = isProductionDeploy()
  ? process.env.STEER_CLIENT_SECERET_SSO
  : process.env.STEER_CLIENT_SECERET_SSO_INTEGRATION;

async function CheckoutOrderConfirmationPage(
  appRoutePageParams: AppRouteCheckoutOrderConfirmationPageParams,
) {
  const { orderId } = await appRoutePageParams.params;
  const pageParams = await appRoutePageParams.searchParams;
  const query = getStringifiedParams(pageParams);

  const cookieStore = await cookies();

  const storedCartId = cookieStore.get(COOKIES.CART_ID)?.value ?? null;
  const ssoTokenInCookie = cookieStore.get(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
  )?.value;
  const csrfTokenInCookie = cookieStore.get(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_CSRF,
  )?.value;
  const redirectUri = cookieStore.get(
    SSO_COOKIE_CONSTANTS.ACCOUNT_REDIRECT,
  )?.value;

  const storedOrderId =
    orderId ?? cookieStore.get(COOKIES.ORDER_ID)?.value ?? null;

  if (!storedCartId || !storedOrderId) {
    notFound();
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const cartOrderRes = await backendGetOrder({
    orderId: storedOrderId,
    extraQueryParams,
  });

  if (!cartOrderRes.isSuccess) {
    const errorStatusCode = cartOrderRes.error?.statusCode || 500;
    if (errorStatusCode === 404) {
      notFound();
    }
    throw cartOrderRes.error;
  }
  if (!cartOrderRes.data) {
    notFound();
  }

  const [cartShippingResponse, cartBillingResponse, cartSummaryResponse] =
    await Promise.all([
      backendGetCartShipping({ cartId: storedCartId }, extraQueryParams),
      backendGetCartBilling({ cartId: storedCartId }, extraQueryParams),
      backendGetCartSummary(
        {
          id: storedCartId,
        },
        extraQueryParams,
      ),
    ]);

  let userDetails: SSOUserIdResponse | null = null;
  let setCookie = async () => {
    'use server';
  };
  const { code, state } = query;

  if (
    !ssoTokenInCookie &&
    csrfTokenInCookie !== undefined &&
    csrfTokenInCookie === state &&
    code &&
    redirectUri
  ) {
    const body = {
      client_id: 'steer',
      client_secret: clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: redirectUri,
    } as SSOTokenInput;

    const res = await backendGetSSOToken(body);
    if (res.isSuccess && res.data) {
      const data = res.data as unknown as SSOTokenResponse;
      if (data.access_token) {
        const userRes = await backendGetUserIdFromSSOToken(data.access_token);
        if (userRes.isSuccess) {
          userDetails = userRes.data;
          setCookie = async () => {
            'use server';

            // Set the SSO token cookie using the cookies() API
            cookieStore.set(
              SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
              data.access_token,
              {
                domain: isLocal ? undefined : SSO_COOKIE_CONSTANTS.DOMAIN,
                maxAge: 86400 * 30,
                path: '/',
                secure: !isLocal,
              },
            );
          };
        }
      }
    }
  } else if (ssoTokenInCookie) {
    const res = await backendGetUserIdFromSSOToken(ssoTokenInCookie);
    if (res.isSuccess) {
      userDetails = res.data;
    }
  }

  const serverData: ServerData = {
    cartId: storedCartId,
    orderId: storedOrderId,
    setCookie,
    siteBilling: cartBillingResponse.isSuccess
      ? cartBillingResponse.data.siteCartBillingResponse
      : null,
    siteCartOrder: cartOrderRes.data,
    siteCartSummaryData: cartSummaryResponse.isSuccess
      ? cartSummaryResponse.data
      : null,
    siteShipping: cartShippingResponse.isSuccess
      ? cartShippingResponse.data.siteCartShippingResponse
      : null,
    userDetails,
  };

  return <OrderConfirmationContainer {...serverData} />;
}

export default CheckoutOrderConfirmationPage;
