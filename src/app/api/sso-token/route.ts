import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

import { backendGetSSOToken } from '~/lib/backend/account/sso-token';
import { backendBootstrap } from '~/lib/backend/bootstrap';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { SSOTokenInput, SSOTokenResponse } from '~/lib/constants/sso.types';
import { isProductionDeploy } from '~/lib/utils/deploy';

const clientSecret = isProductionDeploy()
  ? process.env.STEER_CLIENT_SECERET_SSO
  : process.env.STEER_CLIENT_SECERET_SSO_INTEGRATION;

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const cookieStore = await cookies();
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');

  if (code && state && clientSecret) {
    const ssoTokenInCookie = cookieStore.get(
      SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    )?.value;
    const csrfTokenInCookie = cookieStore.get(
      SSO_COOKIE_CONSTANTS.SIMPLETIRE_CSRF,
    )?.value;
    const redirectUri = cookieStore.get(
      SSO_COOKIE_CONSTANTS.HOME_REDIRECT,
    )?.value;

    if (!redirectUri) {
      return new Response(null, { status: 400 });
    }

    const body = {
      client_id: 'steer',
      client_secret: clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: decodeURIComponent(redirectUri),
    } as unknown as SSOTokenInput;

    if (
      !ssoTokenInCookie &&
      csrfTokenInCookie !== undefined &&
      csrfTokenInCookie === state
    ) {
      const res = await backendGetSSOToken(body);
      if (res.isSuccess && res.data) {
        const data = res.data as unknown as SSOTokenResponse;
        if (data.access_token) {
          const cookieOptions = {
            maxAge: 86400 * 30,
            path: '/',
          };
          cookieStore.set(
            SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
            data.access_token,
            cookieOptions,
          );
          cookieStore.set(
            SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
            data.access_token,
            {
              ...cookieOptions,
              domain: SSO_COOKIE_CONSTANTS.DOMAIN,
            },
          );

          return new Response(null, { status: 204 });
        }
      }
    }
  }

  return new Response(null, { status: 400 });
}
