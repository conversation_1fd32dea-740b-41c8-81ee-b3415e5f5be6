import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendCreateCartAppointment,
  backendDeleteCartAppointment,
  backendGetCartAppointment,
  backendUpdateCartAppointment,
} from '~/lib/backend/checkout/cart-appointment';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function GET(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id, ...rest } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserZip: true,
    query,
  });

  const res = await backendGetCartAppointment(
    { cartId: id, query: rest },
    extraQueryParams,
  );

  if (res.isSuccess) {
    if (res.data.SiteCartAppointment === null) {
      return new Response(null, {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify(res.data.SiteCartAppointment), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (res.error.statusCode !== 404 && res.error.statusCode !== 539) {
    return new Response(null, { status: res.error.statusCode });
  } else {
    return new Response(
      JSON.stringify({ error: res.error, isSuccess: false }),
      {
        status: res.error.statusCode,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserZip: true,
    query,
  });

  const res = await backendCreateCartAppointment(
    {
      cartId: id,
      input: requestBody,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    if (res.data.SiteCartAppointment === null) {
      return new Response(null, {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify(res.data.SiteCartAppointment), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, { status: res.error.statusCode });
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserZip: true,
    query,
  });

  const res = await backendUpdateCartAppointment(
    {
      cartId: id,
      input: requestBody,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    if (res.data.SiteCartAppointment === null) {
      return new Response(null, {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify(res.data.SiteCartAppointment), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, { status: res.error.statusCode });
}

export async function DELETE(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserZip: true,
  });

  const res = await backendDeleteCartAppointment(
    {
      cartId: id,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(null, { status: 204 });
  }

  return new Response(null, { status: res.error.statusCode });
}
