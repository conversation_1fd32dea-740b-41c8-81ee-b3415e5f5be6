import { backendBootstrap } from '~/lib/backend/bootstrap';
import { backendGetBraintreeClientToken } from '~/lib/backend/checkout/braintree-client-token';

export async function GET() {
  await backendBootstrap();

  const res = await backendGetBraintreeClientToken();

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (res.error) {
    return new Response(null, {
      status: res.error.statusCode,
    });
  }

  return new Response(null, { status: 500 });
}
