import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import { backendGetCartKlarna } from '~/lib/backend/checkout/cart-klarna';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendGetCartKlarna(
    { cartId, gateway: 'klarna' },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
